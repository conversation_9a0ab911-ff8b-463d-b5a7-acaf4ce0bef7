import React from 'react';

import { Checkbox, Field, Label } from '@headlessui/react';

import CheckmarkIcon from '../Icons/CheckmarkIcon';
import ValidationError from '../ValidationError';
import DTooltip from '../DTooltip';
import InfoIcon from '../Icons/InfoIcon';

  const DCheckbox = ({
  checked,
  onChange,
  label,
  disabled = false,
  hideError = false,
  tooltipContent,
  className,
  size,
  error,
  customIcon,
  checkboxSize = 'size-5', // Default size
  checkedBgColor = 'data-[checked]:bg-black', // Default checked background
  ...props
}) => {
  let fontSize = 'text-base';
  if (size === 'sm') {
    fontSize = 'text-sm';
  }
  return (
    <div className="flex flex-col gap-size1 w-full" {...props}>
      <Field
        className={`flex items-center gap-2 ${className}`}
        disabled={disabled}
      >
        <Checkbox
          checked={checked}
          onChange={onChange}
          className={`group flex ${checkboxSize} items-center justify-center rounded border bg-white ${checkedBgColor} data-[disabled]:opacity-50`}
          data-testid={`checkbox-${props.name ?? ''}`}
        >
          {customIcon ? (
            <div className="hidden text-white group-data-[checked]:block">
              {customIcon}
            </div>
          ) : (
            <CheckmarkIcon
              width="7"
              className="hidden size-4 text-white group-data-[checked]:block"
            />
          )}
        </Checkbox>
        <Label className={` ${fontSize ? fontSize : 'text-sm'} leading-[160%] hover:cursor-pointer`}>
          <span dangerouslySetInnerHTML={{ __html: label }} />
        </Label>
       {tooltipContent && (
          <DTooltip content={tooltipContent} position="right">
            <InfoIcon className="text-grey-50 size-3" />
          </DTooltip>
        )}
      </Field>
      {!hideError && <ValidationError error={error} />}
    </div>
  );
};

export default DCheckbox;
