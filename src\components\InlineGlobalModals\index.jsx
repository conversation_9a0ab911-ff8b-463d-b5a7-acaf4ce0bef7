import { useEffect, useState } from 'react';
import InlinePlans from '../InlinePlans';
import useModalStore from '@/stores/modal/modalStore';
import { Transition } from '@headlessui/react';
import useDanteApi from '@/hooks/useDanteApi';
import * as plansService from '@/services/plans.service';

/**
 * InlineGlobalModals component
 * 
 * This component provides global modals that overlay their parent container
 * rather than the entire screen. It creates a popup effect within the parent div.
 * It uses the same state management as the GlobalModals component.
 */
const InlineGlobalModals = ({ className = '' }) => {
  const { showInlinePlans, closeInlinePlansModal, inlineCurrentFeature, inlineCurrentFeatureTitle } = useModalStore();
  const { data: tiers, isLoading } = useDanteApi(plansService.getTiers);



  // Prevent parent container scrolling when modal is open
  useEffect(() => {
    const parentContainer = document.querySelector('.grow.overflow-y-auto');
    
    if (parentContainer && showInlinePlans) {
      const originalOverflow = parentContainer.style.overflow;
      parentContainer.style.overflow = 'hidden';
      
      return () => {
        parentContainer.style.overflow = originalOverflow;
      };
    }
  }, [showInlinePlans]);

  // Only show when data is ready
  const shouldShowContent = Boolean(tiers || !isLoading);
  const shouldShowModal = showInlinePlans && shouldShowContent;

  return (
    <div className={`absolute inset-0 ${className} ${!showInlinePlans ? 'hidden' : ''}`}>
      <Transition
        show={shouldShowModal}
        enter="transition-all duration-300"
        enterFrom="opacity-0 scale-95"
        enterTo="opacity-100 scale-100"
        leave="transition-all duration-300"
        leaveFrom="opacity-100 scale-100"
        leaveTo="opacity-0 scale-95"
      >
        {/* Overlay that covers parent container */}
        <div 
          className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-size1 z-10 flex items-center justify-center"
        >
          {/* Modal content with animation */}
          <div 
            className="w-[1100px] animate-fadeInUp"
            onClick={(e) => e.stopPropagation()}
          >
            <InlinePlans
              isVisible={shouldShowModal}
              onClose={closeInlinePlansModal}
              highlightedFeature={inlineCurrentFeature}
              title={inlineCurrentFeatureTitle || 'Upgrade your plan to unlock this feature'}
              className="shadow-xl"
            />
          </div>
        </div>
      </Transition>
    </div>
  );
};

export default InlineGlobalModals; 