# TODO: Make the frontend base url dynamic - VITE_APP_BASE_URL
# TODO: Add all rediret like /embed /share/* /embed/bubble  /share
# TODO: Fix the comment link steps in the other action steps
name: my-preview-app

static_sites:
  - name: deploy-preview-static-site
    github:
      repo: TheDanteAI/dante-frontend-app-v3
      branch: staging
      deploy_on_push: true

    build_command: npm install && npm run build
    output_dir: build


    catchall_document: index.html

    envs:
      - key: VITE_APP_MAINTENANCE_MODE
        value: false
      - key: VITE_APP_USER_ID
        value: 30c0d5f6-c0e2-11ed-afa1-0242ac120002
      - key: VITE_APP_BASE_API
        value: https://api-v2.dante-ai.com/
      - key: VITE_APP_BASE_WEBSOCKET
        value: wss://api-v2.dante-ai.com
      - key: VITE_APP_BASE_URL
        value: https://staging-new.dante-ai.com/
      - key: GENERATE_SOURCEMAP
        value: false
      - key: VITE_APP_GOOGLE_CLIENT_ID
        value: 364701941485-6sd2sh6prlbd1klvaok54ukh1vgu2v0p.apps.googleusercontent.com
      - key: VITE_APP_USERFLOW_TOKEN_STAGING
        value: *****************************
      - key: VITE_APP_USERFLOW_TOKEN_PRODUCTION
        value: *****************************
      - key: VITE_APP_BEAMS_INSTANCE
        value: b3f8adcb-d773-4601-bada-0fb2a4f6891c
      - key: VITE_APP_RETRY_COUNT
        value: 1200
      - key: VITE_APP_AVATURN_API_KEY
        value: W0lkHk-cIuTj9AyyLEOUWQXMC5GqcFkTCUs3wsMR3TEjLriyKhpnQcAzt7psqurz6DgP_JFXG4OlBImtkC-lbg
      - key: VITE_APP_PRODUCT_FRUIT_STAGING
        value: v3VLLOu25Y8dGhdU
      - key: VITE_APP_PRODUCT_FRUIT_PROD
        value: QbhaGc6mAOrccFIb
      - key: VITE_APP_PRODUCT_FRUIT
        value: v3VLLOu25Y8dGhdU
      - key: VITE_APP_DANTE_FAQ_KB_ID
        value: 9c0294ed-8b0e-46b5-9a65-21d689c8f747
      - key: VITE_APP_BASE_URL_OLD_URL
        value: https://staging.dante-ai.com/
      - key: VITE_APP_AMPLITUDE_API_KEY
        value: b5c4b0da85f935cb6bf012fa423d67b9
      - key: VITE_APP_DANTE_SIGNUP_TEAM_MAX_CREDITS
        value: 100
      - key: VITE_APP_DANTE_SIGNUP_TEAM_ROLE_ID
        value: 569273d0-a050-4735-8908-43ee322ec9ac
      - key: VITE_APP_CLARITY_PROJECT_ID
        value: sblxct5emo
      - key: VITE_APP_AI_VOICE_WEBSOCKET
        value: wss://ai-voice.dante-ai.com
      - key: VITE_APP_TURNSTILE_SITEKEY
        value: 0x4AAAAAABe1FXEg9EmBW6rf

ingress:
  rules:
    - match:
        path:
          prefix: /
      component:
        name: deploy-preview-static-site
        preserve_path_prefix: false
        rewrite: ''
    - match:
        path:
          prefix: /embed
      component:
        name: deploy-preview-static-site
        preserve_path_prefix: false
        rewrite: /embed.html
    - match:
        path:
          prefix: /share/*
      component:
        name: deploy-preview-static-site
        preserve_path_prefix: false
        rewrite: /share.html
    - match:
        path:
          prefix: /embed/bubble
      component:
        name: deploy-preview-static-site
        preserve_path_prefix: false
        rewrite: /embed.html
    - match:
        path:
          prefix: /embed/tooltips
      component:
        name: deploy-preview-static-site
        preserve_path_prefix: false
        rewrite: /embed.html
