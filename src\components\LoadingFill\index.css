.fill-container {
    position: relative;
    width: 100%;
    background: #f5f5f5;   /* light “empty” color */
    overflow: hidden;
    border-radius: 8px;
  }
  
  .fill-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(92deg, #06BA63 0%, #27EC8D 100%);
    height: 0%;
    transition: height 0.2s ease-out;
  }
  
  .content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    /* add padding/text styles as needed */
  }
  