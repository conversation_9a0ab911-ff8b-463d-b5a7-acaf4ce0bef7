import './bubble-embed.css';

(function () {
  // Function to track chatbot embed events in dataLayer
  const trackChatbotShared = (embedData) => {
    if (typeof window === 'undefined') return;
    
    window.dataLayer = window.dataLayer || [];
    
    window.dataLayer.push({
      event: 'chatbot_shared',
      chatbot_id: embedData.chatbot_id || '',
      website_url: embedData.website_url || window.location.origin,
      embed_type: 'bubble',
    });
  };
  let embedOpen = false;
  // Default style without dynamic bottom value
  let styleBaseDivChat = 'position: fixed;width: 500px;height: 85dvh;min-height: 600px;max-width: 500px;max-height: 85dvh;bottom: 16px;right: 16px;z-index: 999999;opacity: 0;transform: scale(0);transform-origin: bottom right;transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease-out;pointer-events: none;border: none;border-radius: 16px;box-shadow: 0px 10px 80px 0px rgba(5, 1, 45, 0.10);display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;'
  let chatIframeStyle = 'position: absolute;top: 0;left: 0;width: 100%;height: 100%;border: none;border-radius: 8px;overflow: auto;';
  let styleBaseTooltip = 'position: fixed;width: 300px;bottom: 90px;right: 30px;z-index: 999990;border: none;pointer-events: auto;background: transparent;';
  let styleDivTooltip = 'position: fixed;width: 300px;height: auto;bottom: 90px;right: 30px;z-index: 999990;cursor: pointer;pointer-events: auto;';
  let styleImgWrapper = 'position: fixed; bottom: 24px; right: 24px; z-index: 99999; opacity: 0; transition: opacity 0.5s ease-in-out; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;';
  let greenDotStyle =
    'position: absolute; top: -5px; left: -7px; width: 20px; height: 20px; background: #0DF806; border-radius: 50%; border: 4px solid white; z-index: 99999;';
  let closeDivStyle = 'display: inline-flex; padding: 8px; align-items: center; gap: 10px; border-radius: 100px; background: var(--White, #FFF); flex-shrink: 0; box-shadow: 0px 4px 20px 0px rgba(5, 1, 45, 0.10);  opacity: 1; justify-content: center; cursor: pointer; transition: transform 300ms ease-in-out;box-sizing: border-box;';
  let styleImgDefault =  'display: inline-flex; padding: 8px; align-items: center; box-sizing: border-box; gap: 10px; border-radius: 100px; background: var(--White, #FFF); box-shadow: 0px 4px 20px 0px rgba(5, 1, 45, 0.10); opacity: 0; cursor: pointer; transition: transform 300ms ease-in-out;';

 
  const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
  const vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

  const getConfig = () => {
    const script = document.currentScript;

    const scriptURL = script.src;

    const isConfigInScript = scriptURL.includes('kb_id') && scriptURL.includes('token');

    let urlConfig = '';

    if (isConfigInScript) {
      const url = new URL(scriptURL);
      url.pathname =
        url.searchParams.has('module', 'chatbot') ||
          (url.searchParams.has('kb_id') && url.searchParams.has('token'))
          ? '/embed/bubble/'
          : '/embed/avatar/';
      urlConfig = url.toString();
    } else {
      urlConfig = window.danteEmbed;
    }

    return urlConfig;
  };




  const danteConfig = getConfig();
  const cleanedURL = danteConfig
    ?.replace('chat.helppi-ai.fi', 'app.dante-ai.com')
    ?.replace('chat.challengedevelopgrow.com', 'app.dante-ai.com')
    ?.replace('/embed?', '/embed/?');
  const embedUrl = new URL(cleanedURL);
  
  // Update styleBaseDivChat with the correct bottom value based on kb_id
  if (embedUrl?.searchParams?.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl?.searchParams?.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c') {
    styleBaseDivChat = styleBaseDivChat.replace('bottom: 16px', 'bottom: 90px');
    styleImgWrapper = styleImgWrapper.replace('bottom: 24px', 'bottom: 16px');
    styleImgWrapper = styleImgWrapper.replace('right: 24px', 'right: 16px');
    styleImgWrapper = styleImgWrapper.replace('width: 48px', 'width: 48px');
    styleImgWrapper = styleImgWrapper.replace('height: 48px', 'height: 48px');
  }
  
  const openByDefault = embedUrl.searchParams.get('bubbleopen');
  let cookiesAllowed = false;
  let cookiesEventData = false;
  let promptClickEvent = {};
  let hasSentPrompt = false;

  if (vw < 480) {
    styleImgWrapper += 'bottom: 20px;right: 20px;';
    styleBaseTooltip = 'position: fixed;width: auto;height: auto;bottom: 90px;right: 30px;z-index: 999990;border: none;pointer-events: auto;background: transparent;';
    styleDivTooltip = 'position: fixed;width: calc(100% - 90px);height: auto;bottom: 10px;right: 80px;z-index: 999990;pointer-events: auto;';
    styleBaseDivChat = 'position: fixed;width: 100vw;height: 100dvh;max-width: 100vw;max-height: 100dvh;bottom: 0;right: 0;left: 0;top: 0;z-index: 999999;opacity: 0;transform: scale(0);transform-origin: bottom right;transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease-out;pointer-events: none;border: none;display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;-webkit-overflow-scrolling: touch;';
  } else if (vh < 800) {
    // Adjust for smaller height screens
    styleBaseDivChat = 'position: fixed;width: 400px;height: 85dvh;min-height: 400px;max-width: 400px;max-height: 85dvh; bottom: 90px;right: 30px;z-index: 999999;opacity: 0;transform: scale(0);transform-origin: bottom right;transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease-out;pointer-events: none;border: none;border-radius: 8px;box-shadow: 0px 10px 16px rgba(0, 0, 0, 0.25);display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;';

  }

  let styleOpen = styleBaseDivChat.replace('transform: scale(0)', 'transform: scale(1)').replace('opacity: 0', 'opacity: 1').replace('pointer-events: none', 'pointer-events: auto');

  const closeIconSvg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M4.45499 8.20499C4.66592 7.99431 4.95186 7.87598 5.24999 7.87598C5.54811 7.87598 5.83405 7.99431 6.04499 8.20499L12 14.16L17.955 8.20499C18.058 8.09446 18.1822 8.0058 18.3202 7.94431C18.4582 7.88283 18.6071 7.84976 18.7582 7.8471C18.9093 7.84443 19.0593 7.87222 19.1994 7.9288C19.3395 7.98538 19.4667 8.0696 19.5735 8.17643C19.6804 8.28326 19.7646 8.41051 19.8212 8.55059C19.8778 8.69067 19.9055 8.84072 19.9029 8.99177C19.9002 9.14282 19.8671 9.29179 19.8057 9.42979C19.7442 9.56779 19.6555 9.69199 19.545 9.79499L12.795 16.545C12.584 16.7557 12.2981 16.874 12 16.874C11.7019 16.874 11.4159 16.7557 11.205 16.545L4.45499 9.79499C4.24431 9.58405 4.12598 9.29811 4.12598 8.99999C4.12598 8.70186 4.24431 8.41592 4.45499 8.20499Z" fill="#0F0942"/>
    </svg>
  `;
 const closeIcon = document.createElement('div');

  const mountEmbed = () => {
    const toggleEmbed = (forceOpen) => {
      if (forceOpen) {
        embedOpen = true;
      } else {
        // Toggle the state of embedOpen
        embedOpen = !embedOpen;
      }

      // Check if cookies are allowed before saving to localStorage
      if (cookiesAllowed) {
        localStorage.setItem('embed-chatbot-open', embedOpen);
      }

      // Hide the iframe tooltip
      elemIframeTooltip.style.cssText = 'opacity:0;';

      // Set the style of divTooltipIframe to hide it
      divTooltipIframe.style.cssText = styleDivTooltip + 'pointer-events: none;';
      setTimeout(() => {
        // After 500ms, set the style of divTooltipIframe to completely hide it
        divTooltipIframe.style.cssText = 'display:none;';
        divTooltipIframe.remove();
      }, 500);

      // Check if the embed is open
      if (embedOpen) {
        console.log('embedOpen', embedOpen);
        if (!document.querySelector('#dante_chatbot_iframe')) {
          divChat.appendChild(elemIframe);
        }

        // Set the style of divChat to open
        divChat.style.cssText = styleOpen;

        if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
          updateIcon(true);
        }

        // If the viewport width is less than 480, prevent scrolling
        if (vw < 480) {
          document.body.style['overflow'] = 'hidden';
        }
      } else {
        // Set the style of divChat to closed
        console.log('embedOpen', embedOpen);
        divChat.style.cssText = styleBaseDivChat;

        // Update the icon to show chatbot icon
        updateIcon(false);

        if (vw < 480) {
          document.body.style['overflow'] = 'unset';
        }
      }
    };

    // Function to update the icon based on embedOpen state
    const updateIcon = (isOpen) => {
      // Clear the imageWrapper
      while (imageWrapper.firstChild) {
        if (imageWrapper.firstChild.classList && imageWrapper.firstChild.classList.contains('green-dot')) {
          // Keep the green dot if it exists
          break;
        }
        imageWrapper.removeChild(imageWrapper.firstChild);
      }

      if (isOpen) {
        // Show close icon
       
        closeIcon.style.cssText = closeDivStyle;
        closeIcon.innerHTML = closeIconSvg;
        if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
          closeIcon.style.cssText = closeDivStyle + 'width: 56px; height: 56px;';
        }
        // else{
        //   closeIcon.style.cssText = 'display: inline-flex; padding: 8px; align-items: center; gap: 10px; border-radius: 100px; background: var(--White, #FFF); flex-shrink: 0; box-shadow: 0px 4px 20px 0px rgba(5, 1, 45, 0.10); width: 40px; height: 40px; opacity: 1; justify-content: center; cursor: pointer; transition: transform 300ms ease-in-out;';
        // }
        closeIcon.addEventListener('click', () => {
          toggleEmbed();
        });

        closeIcon.addEventListener('mouseover', () => {
          closeIcon.querySelector('path').setAttribute('fill', '#6351FF');
        });
        closeIcon.addEventListener('mouseout', () => {
          closeIcon.querySelector('path').setAttribute('fill', '#0F0942');
        });
        closeIcon.classList.add('dante-close-icon');
        imageWrapper.appendChild(closeIcon);
      } else {
        // Show chatbot icon
        imageWrapper.appendChild(elemImg);
      }
    };

    const imgHover = () => {
      elemImg.style.transform = 'scale(1.1)';
    };
    const imgDefault = () => {
      elemImg.style.transform = 'scale(1.0)';
    };

    const divTooltipIframe = document.createElement('div');
    const divChat = document.createElement('div');
    const elemIframeTooltip = document.createElement('iframe');
    const elemIframeThirdParty = document.createElement('iframe');

    elemIframeThirdParty.src = `${embedUrl.origin}/thirdparty.html`;
    elemIframeThirdParty.style = 'display:none; opacity:0;';

    const imageWrapper = document.createElement('div');
    imageWrapper.classList.add('dante-image-wrapper');
    imageWrapper.classList.add('animate-fadeIn');
    imageWrapper.style.cssText = styleImgWrapper;

    const greenDot = document.createElement('div');
    greenDot.classList.add('green-dot');
    greenDot.style.cssText = greenDotStyle;

    const urlsWithGreenDot = [
      // 'https://www.dante-ai.com/',
      'https://www.burokoorts.nl/'
    ];

    const currentUrl = window.location.href;
    if (urlsWithGreenDot.some((url) => currentUrl.includes(url))) {
      imageWrapper.appendChild(greenDot);
    }

    const elemImg = document.createElement('img');
    elemImg.alt = 'Chatbot Icon';

    elemImg.style.cssText = styleImgDefault + 'width: 40px; height: 40px;';
    elemImg.addEventListener('click', () => {
      toggleEmbed();
    });
    elemImg.addEventListener('mouseover', imgHover);
    elemImg.addEventListener('mouseout', imgDefault);
    elemImg.classList.add('dante-skeleton');
    elemImg.classList.add('dante-embed-icon');

    if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
      elemImg.style.cssText = 'display: inline-flex; padding: 8px; align-items: center; gap: 10px; border-radius: 100px; background: var(--White, #FFF); box-shadow: 0px 4px 20px 0px rgba(5, 1, 45, 0.10); width: 40px; height: 40px; opacity: 0; cursor: pointer; transition: transform 300ms ease-in-out;'; 
    }

    const elemIframe = document.createElement('iframe');
    elemIframe.id = 'dante_chatbot_iframe';
    elemIframe.src = cleanedURL;
    elemIframe.title = 'Dante AI Chatbot';
    elemIframe.allow = 'clipboard-write *; microphone *';
    elemIframe.style.cssText = chatIframeStyle;
    elemIframe.setAttribute('scrolling', 'auto');

      divChat.style.cssText = styleBaseDivChat + 'transition: width 0.5s ease, height 0.5s ease;';
    divChat.classList.add('dante-embed-chat');

    document.body.appendChild(divChat);

    fetch(
      `${import.meta.env.VITE_APP_BASE_API}knowledge-bases/customization/shared?kb_id=${embedUrl.searchParams.get('kb_id')}&token=${embedUrl.searchParams.get('token')}`
    )
      .then((response) => response.json())
      .then((data) => {
        console.log('API Response Data:', {
          show_welcome_message_as_tooltip: data?.show_welcome_message_as_tooltip,
          kb_id: embedUrl.searchParams.get('kb_id'),
          viewport_width: vw,
          tooltipShown: localStorage.getItem('tooltipShown'),
          full_data: data
        });
        
        // Track chatbot embed event
        trackChatbotShared({
          chatbot_id: embedUrl.searchParams.get('kb_id') || '',
          website_url: window.location.origin,
        });

        if (data?.show_welcome_message_as_tooltip) {
          const tooltipShown = localStorage.getItem('tooltipShown') === 'true';

          if (!(embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && vw < 480) && !tooltipShown) {
            // Only create and setup tooltip iframe if show_welcome_message_as_tooltip is true
            const urlTooltip = new URL(
              danteConfig
                .replace('chat.helppi-ai.fi', 'app.dante-ai.com')
                .replace('/embed/tooltips?', '/embed/tooltips/?')
                .replace('/embed/bubble/?', '/embed/tooltips/?')
                .replace('/embed/bubble?', '/embed/tooltips/?')
            );
            if (!['chat.dante-ai.com', 'app.dante-ai.com', 'dante-ai.com'].includes(urlTooltip.hostname)) {
              if (urlTooltip.pathname === '/') {
                urlTooltip.pathname = '/tooltips/';
              }
            }

            elemIframeTooltip.src = urlTooltip.toString();
            elemIframeTooltip.title = 'Dante AI Prompts';
            elemIframeTooltip.style.cssText = styleBaseTooltip;
            elemIframeTooltip.id = 'dante_tooltip_iframe';
            divTooltipIframe.style.cssText = styleDivTooltip;
            divTooltipIframe.classList.add('dante-embed-tooltips');
            divTooltipIframe.appendChild(elemIframeTooltip);

            elemIframeTooltip.addEventListener('click', () => {
              toggleEmbed();
            });

            document.body.appendChild(divTooltipIframe);
          }
        }

        document.body.appendChild(elemIframeThirdParty);
      
      

      if (data?.bubble_size) {
        let bubbleSize;
        let bottomSize;
        switch (data.bubble_size) {
          case 'SMALL':
            bubbleSize = '40px';
            if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
              bottomSize = '70px';
            }
            break;
          case 'MEDIUM':
            bubbleSize = '48px';
            if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
              bottomSize = '80px';
            }
            break;
          case 'LARGE':
            bubbleSize = '56px';
            if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
              bottomSize = '90px';
            }
            break;
          case 'EXTRA_LARGE':
            bubbleSize = '100px';
            break;
          default:
            bubbleSize = '48px';
            if(embedUrl.searchParams.get('kb_id') === '996c0c59-c322-4194-a016-a85f45c365ed' || embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c'){
              bottomSize = '80px';
            }
            break;
        }

      
        imageWrapper.style.width = bubbleSize;
        imageWrapper.style.height = bubbleSize;
        closeIcon.style.cssText = closeDivStyle + `width: ${bubbleSize}; height: ${bubbleSize};`;
        styleBaseDivChat = styleBaseDivChat.replace('bottom: 16px', `bottom: ${bottomSize}`);

        elemImg.style.cssText = `width: ${bubbleSize}; height: ${bubbleSize};` + styleImgDefault;
    

        styleImgWrapper = styleImgWrapper.replace(/width: \d+px/, `width: ${bubbleSize}`);
        styleImgWrapper = styleImgWrapper.replace(/height: \d+px/, `height: ${bubbleSize}`);
        
      
      }

        if (data.chatbot_icon !== undefined && data.chatbot_icon !== null && data.chatbot_icon !== '') {
          elemImg.src = data.chatbot_icon;
          elemImg.classList.remove('dante-skeleton');
        } else {
          elemImg.src = 'https://chat.dante-ai.com/btn-embed.png';
          elemImg.classList.remove('dante-skeleton');
        }

        if(!embedOpen){
          imageWrapper.appendChild(elemImg);
        } else {
          // If embed is open by default, show the close icon
          updateIcon(true);
        }
        document.body.appendChild(imageWrapper);
        
        // Fade in the image wrapper after a short delay
        setTimeout(() => {
          imageWrapper.style.opacity = '1';
          elemImg.style.opacity = '1';
        }, 100);
      })
      .catch(() => { });

    const pulseIn = () => {
      // Set opacity directly without pulsing animation
      elemImg.style.opacity = '1';
    };

    // No need for pulseOut since we're not pulsing anymore
    // Just call pulseIn directly
    pulseIn();
    
    if (openByDefault === 'true') {
      setTimeout(() => toggleEmbed(), 1000);
    }

    const handleThirdPartyEvent = (event) => {
      if (event.data.eventData === 'allow_third_party_cookie') {
        cookiesAllowed = true;
        if (localStorage.getItem('embed-chatbot-open') === 'true') {
          const lastInteraction = new Date(localStorage.getItem('embed-chatbot-last-interaction'));
          const interactionTime = lastInteraction.getTime() + 5 * 60000;
          const currentTime = new Date().getTime();

          if (interactionTime > currentTime) {
            setTimeout(() => toggleEmbed(true), 1000);
          }
        }
        cookiesEventData = event.data;
      }
    };

    const handleMessages = (event) => {
      let tooltipContainer;
      let tooltipIframe;

      switch (event.data.eventType) {
        case 'siteReady':
          {
            const chatIframe = document.querySelector('#dante_chatbot_iframe');
            chatIframe?.contentWindow.postMessage(cookiesEventData, '*');

            // Send parent width immediately when iframe is ready
            chatIframe?.contentWindow.postMessage({
              eventType: 'parentWidthResponse',
              width: window.innerWidth
            }, '*');

            setTimeout(() => {
              if (Object.keys(promptClickEvent).length > 0 && !hasSentPrompt) {
                hasSentPrompt = true;
                chatIframe?.contentWindow.postMessage(promptClickEvent, '*');
                promptClickEvent = {};
              }
            }, 250);
            break;
          }
        case 'chatbotCloseClick':
          toggleEmbed();
          break;

        case 'tooltipCloseClick':
          // Ensure both the container and iframe are removed
          tooltipContainer = document.querySelector('.dante-embed-tooltips');
          tooltipIframe = document.querySelector('#dante_tooltip_iframe');

          if (tooltipContainer) {
            tooltipContainer.style.cssText = 'display:none;';
            tooltipContainer.remove();
          }

          if (tooltipIframe) {
            tooltipIframe.style.cssText = 'display:none;';
            tooltipIframe.remove();
          }
          break;
        case 'promptClick':
          toggleEmbed();

          promptClickEvent = {
            eventType: 'promptClick',
            eventData: event.data.eventData
          };

          setTimeout(() => {
            const chatIframe = document.querySelector('#dante_chatbot_iframe');
            if (chatIframe && chatIframe.contentWindow) {
              chatIframe.contentWindow.postMessage(promptClickEvent, '*');
              promptClickEvent = {};
            }
          }, 500);

          break;
        case 'tooltipClick':
          toggleEmbed();
          break;
        case 'bubbleInteractionDate':
          if (cookiesAllowed) {
            localStorage.setItem('embed-chatbot-last-interaction', event.data.eventData);
          }
          break;
        case 'thirdparty':
          handleThirdPartyEvent(event);
          break;
        case 'tooltipContainerHeight':
          {
            const tooltipIframe = document.querySelector('#dante_tooltip_iframe');
            if (tooltipIframe) {
              tooltipIframe.style.height = event.data.eventData + 'px';
            }
            break;
          }
        case 'requestParentWidth':
          {
            const chatIframe = document.querySelector('#dante_chatbot_iframe');
            if (chatIframe && chatIframe.contentWindow) {
              chatIframe.contentWindow.postMessage({
                eventType: 'parentWidthResponse',
                width: window.innerWidth
              }, '*');
            }
            break;
          }
        default:
      }
    };

    window.addEventListener('message', handleMessages);
    
    // Listen for window resize and send updated width to iframe
    window.addEventListener('resize', () => {
      const chatIframe = document.querySelector('#dante_chatbot_iframe');
      if (chatIframe && chatIframe.contentWindow) {
        chatIframe.contentWindow.postMessage({
          eventType: 'parentWidthResponse',
          width: window.innerWidth
        }, '*');
      }
    });
  };
  const start = (t) => {
    return (
      document.attachEvent ? 'complete' === document.readyState : 'loading' !== document.readyState
    )
      ? t()
      : document.addEventListener('DOMContentLoaded', t);
  };

  start(mountEmbed);
})();
