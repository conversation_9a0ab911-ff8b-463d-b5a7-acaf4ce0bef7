import { useState, useEffect } from 'react';
import LogoType from './LogoType';
import GrayscaleLogo from '@/assets/logo/logo-symbol-grayscale.png';
import ColorLogo from '@/assets/logo/logo-symbol-color.svg';
import clsx from 'clsx';

const DFullLogo = ({ mode, variant, size = 'md' }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  let sizeSymbolClass = 'size-auto object-center';
  let sizeTypeClass = 'h-4';
  let containerHeight = 'h-5';
  let containerWidth = 'w-[77px]'; // Width of logo + text with proper spacing
  let symbolSize = 'w-4 h-4';

  if (size === 'xs') {
    sizeSymbolClass = 'size-3 object-center';
    sizeTypeClass = 'h-3 w-[40px]';
    containerHeight = 'h-3';
    containerWidth = 'w-[55px]';
    symbolSize = 'w-3 h-3';
  }
  if (size === 'lg') {
    sizeSymbolClass = 'size-6 object-center';
    sizeTypeClass = 'h-6';
    containerHeight = 'h-6';
    containerWidth = 'w-[100px]';
    symbolSize = 'w-6 h-6';
  }

  // Preload the image
  useEffect(() => {
    const img = new Image();
    img.src = variant === 'grayscale' ? GrayscaleLogo : ColorLogo;
    img.onload = () => setImageLoaded(true);
  }, [variant]);

  const symbol = () => {
    if (!imageLoaded) {
      // Return a placeholder with the same dimensions
      return <div className={`${symbolSize} bg-gray-100 rounded-full animate-pulse`}></div>;
    }

    if (variant === 'grayscale') {
      return <img src={GrayscaleLogo} className={sizeSymbolClass} alt="Dante Logo" />;
    }
    return <img src={ColorLogo} className={sizeSymbolClass} alt="Dante Logo" />;
  };

  const containerClasses = clsx(
    'transition-all duration-150 flex gap-[4px] items-center',
    containerHeight,
    containerWidth,
    // 'justify-center' // Center the content horizontally
  );

  if (mode === 'dark') {
    return (
      <div className={containerClasses}>
        {symbol()}
        {imageLoaded && <LogoType className={clsx(sizeTypeClass, 'text-[#fff]/85')} />}
      </div>
    );
  }
  return (
    <div className={containerClasses}>
      {symbol()}
      {imageLoaded && (
        <LogoType
          className={clsx(sizeTypeClass, 'text-[var(--dt-color-element-100)]')}
        />
      )}
    </div>
  );
};

export default DFullLogo;
