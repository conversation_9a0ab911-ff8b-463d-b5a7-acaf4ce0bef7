// pages/TestPage.jsx
import React, { useState, useRef, useEffect } from "react";
import OnboardingLayout from "@/layouts/OnboardingLayout";
import DFullLogo from "@/components/Global/DLogo/DFullLogo";
import "./index.css";
import { useNavigate } from "react-router-dom";
import { confirmUrl } from "@/services/onboarding.service";
import { Turnstile } from '@marsidev/react-turnstile';
import LoadingSpinner from "@/components/Global/Icons/LoadingSpinner";
import StarOnboardingIcon from "@/components/Global/Icons/StarOnboardingIcon";
import PackageIcon from "@/components/Global/Icons/PackageIcon";
import GlobalIcon from "@/components/Global/Icons/GlobalIcon";
import NonentrySignIcon from "@/components/Global/Icons/NonentrySignIcon";
import CreditCardIcon from "@/components/Global/Icons/CreditCardIcon";
import ArrowRight from "@/components/Global/Icons/ArrowRight";




export default function NewOnboarding() {
  const testimonials = [{
    id: 1,
    icon: <GlobalIcon/>,
    description: '100,000+ businesses '
  },
  {
    id: 2,
    icon: <PackageIcon/>,
    description: 'Forever free available'
  },
  {
    id: 3,
    icon: <div className="flex gap-2"><NonentrySignIcon/> <div className="hidden lg:block md:block"><CreditCardIcon/></div></div>,
    description: 'No credit card'
  },
  {
    id: 4,
    icon: <StarOnboardingIcon/> ,
    description: '4.7/5 average rating'
  }]

  const [url, setUrl] = useState('');
  const navigate = useNavigate();
  const [captchaToken, setCaptchaToken] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const captchaRef = useRef(null);
  const [urlError, setUrlError] = useState('');
  const [isValidatingUrl, setIsValidatingUrl] = useState(false);

  const isCaptchaEnabled = () => {
    const siteKey = import.meta.env.VITE_APP_TURNSTILE_SITEKEY;
    return siteKey && siteKey !== 'YOUR_SITE_KEY_HERE' && siteKey !== 'DISABLED';
  };

  const isCaptchaSuccessful = () => {
    const enabled = isCaptchaEnabled();
    const hasToken = !!captchaToken;
    return !enabled || hasToken;
  };

  const resetCaptcha = () => {
    if (isCaptchaEnabled() && captchaRef.current) {
      try {
        captchaRef.current.reset();
        setCaptchaToken('');
        setCaptchaError('');
      } catch (error) {
        setCaptchaToken('');
        setCaptchaError('');
      }
    }
  };

  const handleStart = async () => {
    // If URL is empty, allow the button to be clicked but display error
    if (!url.trim()) {
      setUrlError('Please enter a URL');
      return;
    }

    // Check captcha validation
    if (isCaptchaEnabled() && !captchaToken) {
      setCaptchaError('Please complete the CAPTCHA verification');
      return;
    }

    // Clear any previous errors
    setUrlError('');
    setCaptchaError('');
    setIsValidatingUrl(true);

    try {
      // Validate URL with backend
      const response = await confirmUrl(url);

      if (response.status === 200) {
        // URL is valid, use the confirmed URL from backend
        const confirmedUrl = response.data.url || url;

        // Navigate to the next step with the validated URL
        navigate('/create-account/setting-up', { state: { url: confirmedUrl, captchaToken } });
      }
    } catch (error) {
      // Handle 400 error or other validation errors
      const errorMessage = error.response?.data?.detail || 'Invalid URL. Please check and try again.';
      setUrlError(errorMessage);
    } finally {
      setIsValidatingUrl(false);
    }
  };

  // Global enter key handler
  useEffect(() => {
    const handleGlobalKeyDown = (event) => {
      console.log('event', event);
      // Only trigger if Enter is pressed and we're not in an input field
      if (event.key === 'Enter') {
        // Only trigger if URL is not empty and we're not currently validating
        if (url.trim() && !isValidatingUrl) {
          handleStart();
        }
      }
    };

    // Add event listener to document
    document.addEventListener('keydown', handleGlobalKeyDown);

    // Cleanup on unmount
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [url, isValidatingUrl]); // Dependencies for the effect

  return (
    <OnboardingLayout showLoginButton={true} containerClassName="min-h-screen overflow-hidden relative" navClassName="px-2">

      <div className="flex flex-col  md:flex-col lg:flex-row md:justify-start  lg:justify-center items-center min-h-[100vh] mt-[100px] lg:mt-0">
        <main className="relative z-10 h-full flex items-center justify-center px-[20px] py-[20px]  lg:py-[100px]">
        <div className="flex flex-col md:flex-col lg:flex-row lg:justify-center items-center max-w-[75rem] mx-auto gap-[128px] h-[592px] ">
            <div className="flex flex-col gap-[40px] lg:gap-[80px] items-center  flex-1 ">
                <div className="flex flex-col gap-[48px]">
                    <div className="flex flex-col gap-[16px]">
                        <h1 className=" text-[28px] leading-[140%] font-sora font-bold tracking-[-0.56px]">Let's build your AI agent in less than 60 seconds  </h1>
                        <p className="text-base leading-[160%] font-[Inter]">Watch the magic happen! Your website becomes an AI-powered growth machine that captures leads and helps customers 24/7.</p>
                    </div>
                    <div className="flex flex-col">
                        <div className="flex flex-col gap-2">
                            <p className="text-[#5C5A6E] text-base leading-[160%] font-[Inter]">Web page URL:</p>
                            <input
                                type="text"
                                className={`
                                    flex
                                    h-14
                                    px-4
                                    py-2.5
                                    items-center
                                    gap-2.5
                                    self-stretch
                                    rounded-lg
                                    border
                                    ${urlError ? 'border-red-500' : 'border-[#D1CFE2]'}
                                    bg-white
                                    font-[Inter]
                                `}
                                placeholder="Enter your web page URL"
                                value={url}
                                onChange={e => {
                                    setUrl(e.target.value);
                                    // Clear errors when user starts typing
                                    if (urlError) {
                                        setUrlError('');
                                    }
                                    if (captchaError) {
                                        setCaptchaError('');
                                    }
                                }}
                                onKeyDown={e => {
                                    if (e.key === 'Enter') {
                                        handleStart();
                                    }
                                }}
                                />
                            {/* Error display area - always reserve space */}
                            <div className="min-h-6 animate-fadeIn">
                                {urlError && (
                                    <p className="text-red-500 text-sm leading-[160%] font-[Inter]">{urlError}</p>
                                )}
                            </div>
                                {isCaptchaEnabled() && (
                                  <div className="mb-2">
                                    <Turnstile
                                      ref={captchaRef}
                                      siteKey={import.meta.env.VITE_APP_TURNSTILE_SITEKEY}
                                      options={{
                                        action: 'onboarding',
                                        cData: 'onboarding',
                                        theme: 'light',
                                        size: 'flexible',
                                      }}
                                      onSuccess={(token) => {
                                        setCaptchaToken(token);
                                        setCaptchaError('');
                                      }}
                                      onError={(error) => {
                                        setCaptchaError('CAPTCHA verification failed. Please try again.');
                                        setCaptchaToken('');
                                      }}
                                      onExpire={() => {
                                        setCaptchaToken('');
                                        setCaptchaError('CAPTCHA expired. Please verify again.');
                                      }}
                                    />
                                    <div className="min-h-4 animate-fadeIn">
                                      {captchaError && (
                                        <p className="text-red-500 text-sm leading-[160%] font-[Inter]">{captchaError}</p>
                                      )}
                                    </div>
                                  </div>
                                )}
                        </div>
                        {/* captcha */}
                        <button
                            className="
                                flex
                                h-14
                                px-6
                                py-2.5
                                flex-col
                                justify-center
                                items-center
                                self-stretch
                                rounded-lg
                                border-2
                                border-[#2B13FB]
                                bg-[#6351FF]
                                shadow-[0_0_3px_2px_rgba(255,255,255,0.3),0_4px_20px_0_rgba(88,69,252,0.4)]
                                text-base
                                text-white
                                leading-[150%]
                                font-[Inter]
                                font-medium
                            "
                            onClick={handleStart}
                            data-testid="start-the-magic-button"
                                >
                                {isValidatingUrl ? (
                                    <div className="flex items-center gap-2">
                                        <LoadingSpinner className="h-5 w-5" />
                                        Validating URL
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-1 ">
                                        Start the Magic
                                        <ArrowRight className="h-5 w-5 pt-0.5" />
                                      
                                    </div>
                                )}
                            </button>
                           
                    </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-[48px] gap-y-[16px] w-full pb-[140px] lg:pb-0">
                    {testimonials.map((testimonial) => (
                        <div key={testimonial.id} className="flex gap-4 items-start">
                            <span className="text-xl">{testimonial.icon}</span>
                            <p className="text-base leading-[160%] font-[Inter]">{testimonial.description}</p>
                        </div>
                    ))}

                </div>
            </div>
             <div className="relative w-full h-[592px] top-[-150px] lg:top-0  bg-white rounded-size2 flex-shrink-0 md:flex-shrink-0 lg:flex-1">
                <div className="pt-[20px] pl-[24px] pb-[16px] pr-[18px]">
                    <DFullLogo />
                </div>
                <div className="flex flex-col gap-[24px] px-[24px] py-[20px]">
                    <p className="text-base leading-[160%] font-[Inter]">Hey there! I'm Dante AI. I'm excited to learn about your business and become your smartest team member.</p>
                    <p className="text-base leading-[160%] font-[Inter]">Right now I'm like a blank canvas, ready to absorb everything about your company. What's your website URL so I can start getting to know you?</p>
                    <p className="text-base leading-[160%] font-[Inter]">I'll analyze <b>one page</b> now to get started. Once you sign up, you can add more web pages, documents, media and other files.</p>
                </div>
            </div>
        </div>

        
      </main>

      {/* right‑hand fill (if you still want your 30%‑wide column) */}
      <aside className="w-full h-[592px] mt-[300px] md:mt-[200px] lg:w-[35vw] lg:mt-0 lg:h-full lg:min-h-screen webflow-bg lg:fixed lg:right-0 lg:top-0 lg:bottom-0 z-0 lg:bg-transparent" >

      </aside>
      </div>
    </OnboardingLayout>
  );
}
