import React from 'react';
import ReactDOM from 'react-dom/client';
import { HelmetProvider } from 'react-helmet-async';

import App from './App';
import { setupChunkLoadingErrorHandler } from '@/utils/chunkLoadingErrorHandler';
import ChunkLoadErrorBoundary from '@/components/ChunkLoadErrorBoundary';

import './index.css';

const root = ReactDOM.createRoot(document.getElementById('root'));
const helmetContext = {};

// Setup global chunk loading error handler
if (window.location.hostname !== 'localhost' && window.location.hostname !== 'bs-local.com') { 
  setupChunkLoadingErrorHandler();
}

root.render(
  <ChunkLoadErrorBoundary>
    <HelmetProvider context={helmetContext}>
      <App />
    </HelmetProvider>
  </ChunkLoadErrorBoundary>
);
