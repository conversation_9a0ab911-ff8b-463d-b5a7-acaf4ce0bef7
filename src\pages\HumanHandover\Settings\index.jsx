import DProfileImage from '@/components/DProfileImage';
import CategoryIcon from '@/components/Global/CategoryIcon';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DSwitch from '@/components/Global/DSwitch';
import DInput from '@/components/Global/DInput/DInput';
import DInputBlock from '@/components/Global/DInput/DInputBlock';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import EditIcon from '@/components/Global/Icons/EditIcon';
import useDanteApi from '@/hooks/useDanteApi';
import { getDefaultImages } from '@/services/user.service';
import { useEffect, useState } from 'react';
import AddCategory from './AddCategory';
import EditCategory from './EditCategory';
import clsx from 'clsx';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import * as humanHandoverService from '@/services/humanHandover';
import * as humanHandoverOrganizationService from '@/services/human-handover-organization';
import DLoading from '@/components/DLoading';
import useToast from '@/hooks/useToast';
import { useParams } from 'react-router-dom';
import * as userService from '@/services/user.service';

const HumanHandoverSettings = () => {
  const { organization_id } = useParams();

  const {
    categories,
    updateCategories,
    userOrganization,
    setUserOrganization,
    fileUploadSetting,
    setFileUploadSetting,
  } = useHumanHandoverStore();
  const { data: defaultImages, isLoading: isLoadingDefaultImages } =
    useDanteApi(getDefaultImages);
  const { addSuccessToast } = useToast();

  const {
    data: dataCategories,
    isLoading: isLoadingCategories,
    refetch: refetchCategories,
  } = useDanteApi(
    humanHandoverService.getCategoriesByOrganizationId,
    [],
    {},
    organization_id
  );

  const {
    data: dataNotificationSettings,
    isLoading: isLoadingNotificationSettings,
    refetch: refetchNotificationSettings,
  } = useDanteApi(
    humanHandoverService.getNotificationSettings,
    [],
    {},
    organization_id
  );
  const {
    data: dataFileUploadSetting,
    isLoading: isLoadingFileUploadSetting,
    refetch: refetchFileUploadSetting,
  } = useDanteApi(
    humanHandoverService.getFileUploadSetting,
    [],
    {},
    organization_id
  );
  const {
    data: dataUserOrganization,
    isLoading: isLoadingUserOrganization,
    refetch: refetchUserOrganization,
  } = useDanteApi(
    humanHandoverOrganizationService.getCurrentUserInfo,
    [],
    {},
    organization_id
  );

  const [agentAvatarUrl, setAgentAvatarUrl] = useState(
    userOrganization.image_url
  );
  const [agentAvatarFile, setAgentAvatarFile] = useState(null);
  const [agentName, setAgentName] = useState(userOrganization.name);
  const [isPending, setIsPending] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState(
    dataNotificationSettings?.results
  );
  const [allowFileUpload, setAllowFileUpload] = useState(null);

  const [isEditing, setIsEditing] = useState(false);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isEditingCategory, setIsEditingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [isDeletingCategory, setIsDeletingCategory] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState(null);
  const [isDeletingCategoryLoading, setIsDeletingCategoryLoading] = useState(false);

  const handleImageChange = (name, file, ...rest) => {
    if (file instanceof File) {
      if (name === 'profile_image') {
        setAgentAvatarFile(file);
        setAgentAvatarUrl(URL.createObjectURL(file));
      }
    } else {
      if (name === 'profile_image') {
        setAgentAvatarFile(null);
        setAgentAvatarUrl(file);
      }
    }
  };

  const handleNotificationSettingChange = (index, type) => {
    const newNotificationSettings = [...notificationSettings];
    newNotificationSettings[index][type] =
      !newNotificationSettings[index][type];
    setNotificationSettings(newNotificationSettings);
  };

  const handleAddCategory = async ({ name, color, description }) => {
    try {
      const response = await humanHandoverService.createCategory({
        name,
        color,
        description,
        organization_id: organization_id,
        default: false,
      });
      if (response.status === 200) {
        refetchCategories();
        setIsAddingCategory(false);
        addSuccessToast({
          message: 'Category created successfully',
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleEditCategory = async ({ name, color, description }) => {
    try {
      const response = await humanHandoverService.updateCategory(
        editingCategory.id,
        { name, color, description, organization_id: organization_id }
      );
      if (response.status === 200) {
        refetchCategories();
        setIsEditingCategory(false);
        addSuccessToast({
          message: 'Category updated successfully',
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const transformNotificationSettingsToUpdate = () => {
    const newNotificationSettings = {};
    notificationSettings.forEach((setting) => {
      newNotificationSettings[`${setting.id}_email`] = setting.email;
      newNotificationSettings[`${setting.id}_push`] = setting.push;
    });
    return newNotificationSettings;
  };

  const transformNotificationSettingsToArray = (data) => {
    const newNotificationSettings = [];
    const settingMap = {
      new_chat_request: 'New chat request',
      new_chat_assigned_to_me: 'New chat assigned to me',
      new_message: 'New message',
      // chat_marked_as_solved_by_visitor: 'Chat marked as solved by visitor'
    };
    
    Object.keys(settingMap).forEach(key => {
      if (key + '_email' in data || key + '_push' in data) {
        newNotificationSettings.push({
          id: key,
          label: settingMap[key],
          email: data[key + '_email'] || false,
          push: data[key + '_push'] || false,
        });
      }
    });
    
    return newNotificationSettings;
  };

  const handleEditAccount = async () => {
    const newNotificationSettings = transformNotificationSettingsToUpdate();
    try {
      setIsPending(true);
      let profile_image = agentAvatarUrl;
      if (agentAvatarFile instanceof File) {
        const formData = new FormData();
        formData.append('file', agentAvatarFile);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          profile_image = response?.data?.url;
        }
      }

      const responseNotification =
        await humanHandoverService.updateNotificationSettings(
          organization_id,
          newNotificationSettings
        );
      const responseUserOrganization =
        await humanHandoverOrganizationService.updateUserOrganization(
          organization_id,
          {
            name: agentName,
            image: profile_image,
          }
        );
      const responseFileUpload =
        await humanHandoverService.updateFileUploadSetting(
          organization_id,
          allowFileUpload
        );
      if (
        responseNotification.status === 200 &&
        responseUserOrganization.status === 200 &&
        responseFileUpload.status === 200
      ) {
        refetchNotificationSettings();
        refetchUserOrganization();
        refetchFileUploadSetting();
        setFileUploadSetting(allowFileUpload); // Update the store
        setAgentAvatarFile(null);
        addSuccessToast({
          message: 'Account and notification settings updated successfully',
        });
      }
      setIsEditing(false);
    } catch (error) {
      console.error(`Error updating account: ${error}`);
    } finally {
      setIsPending(false);
    }
  };

  const handleDeleteCategory = async () => {
    setIsDeletingCategoryLoading(true);
    try {
      const response = await humanHandoverService.deleteCategory(
        deletingCategory.id
      );
      if (response.status === 200) {
        refetchCategories();
        setIsDeletingCategory(false);
        addSuccessToast({
          message: 'Category deleted successfully',
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsDeletingCategoryLoading(false);
    }
  };

  useEffect(() => {
    if (dataCategories?.results?.length > 0) {
      updateCategories(dataCategories?.results);
    }
  }, [dataCategories]);

  useEffect(() => {
    if (dataNotificationSettings) {
      setNotificationSettings(
        transformNotificationSettingsToArray(dataNotificationSettings)
      );
    }
  }, [dataNotificationSettings]);

  useEffect(() => {
    if (dataUserOrganization) {
      setUserOrganization(dataUserOrganization);
    }
  }, [dataUserOrganization]);

  useEffect(() => {
    if (userOrganization) {
      setAgentAvatarUrl(userOrganization.image);
      setAgentName(userOrganization.name);
    }
  }, [userOrganization]);

  useEffect(() => {
    if (dataFileUploadSetting !== undefined && dataFileUploadSetting !== null) {
      setAllowFileUpload(dataFileUploadSetting);
      setFileUploadSetting(dataFileUploadSetting);
    }
  }, [dataFileUploadSetting, setFileUploadSetting]);

  if (
    isLoadingCategories ||
    isLoadingNotificationSettings ||
    isLoadingDefaultImages ||
    isLoadingUserOrganization ||
    isLoadingFileUploadSetting
  ) {
    return <DLoading show={true} />;
  }

  return (
    <div className="bg-white h-[1px] grow p-size5 md:p-size5 flex flex-col gap-size4 md:gap-size5 overflow-y-auto scrollbar">
    
      <div className="flex flex-col md:flex-row gap-size5 max-w-full md:max-w-[80%]">
        <div className="flex rounded-size1 p-size5 flex-col gap-size5 bg-grey-2 grow-0 h-auto w-full">
          <div className="flex items-center justify-between">
            <p className="text-xl font-medium tracking-tight">
              Account Settings
            </p>
            <DButton
              variant="grey"
              onClick={() => {
                setIsEditing(!isEditing);
                if (isEditing) {
                  setAgentName(userOrganization.name);
                  setAgentAvatarUrl(userOrganization.image);
                }
              }}
            >
              {isEditing ? 'Cancel' : 'Edit'}
            </DButton>
          </div>
          <div className="w-full h-[1px] bg-grey-5"></div>
          <div className="flex flex-col gap-size1">
            <DProfileImage
              isRounded={true}
              label="Avatar"
              name="avatar"
              imageUrl={agentAvatarUrl}
              defaultImages={defaultImages?.results}
              handleImageChange={(file) =>
                handleImageChange('profile_image', file)
              }
              imageFile={agentAvatarFile}
              showDefaultImages={isEditing}
              readOnly={!isEditing}
              setImageError={(e) => {
                console.log(e);
              }}
              // error={errors?.profile_image}
              maxSizeFile={150 * 1024}
              // required
            />
          </div>
          <DInputBlock label="Name" shouldGrow={false}>
            <DInput
              name="name"
              type="text"
              placeholder="Enter your name"
              disabled={!isEditing}
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
            />
          </DInputBlock>
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <p className="text-sm font-medium tracking-tight">
                Allow file uploads during human handover
              </p>
              <p className="text-xs text-grey-20 tracking-tight">
                Enable visitors to upload files when chatting with agents
              </p>
            </div>
            <DSwitch
              checked={allowFileUpload === true}
              onChange={(checked) => setAllowFileUpload(checked)}
              disabled={!isEditing || isLoadingFileUploadSetting || allowFileUpload === null}
            />
          </div>
          <div className="w-full h-[1px] bg-grey-5"></div>
          <p className="text-lg font-medium tracking-tight">
            Notification settings
          </p>
          <div className="flex flex-col gap-size2 ">
            {notificationSettings?.map((setting, index) => (
              <div
                className="flex items-center justify-between h-8"
                key={index}
              >
                <p className="text-sm font-regular tracking-tight">
                  {setting.label}
                </p>
                {isEditing ? (
                  <div className="flex items-center gap-size1">
                    <DCheckbox
                      label="Email"
                      checked={setting.email}
                      onChange={() =>
                        handleNotificationSettingChange(index, 'email')
                      }
                    />
                    <DCheckbox
                      label="Push"
                      checked={setting.push}
                      onChange={() =>
                        handleNotificationSettingChange(index, 'push')
                      }
                    />
                  </div>
                ) : (
                  <p className="text-xs text-grey-20 tracking-tight">
                    {setting.email ? 'Email' : ''}{' '}
                    {setting.email && setting.push ? ' and ' : ''}{' '}
                    {setting.push ? 'Push' : ''}
                    {!setting.email && !setting.push ? 'No' : ''} Notifications
                  </p>
                )}
              </div>
            ))}
          </div>
          <div
            className={clsx(
              'w-full items-center gap-size1 transition-all duration-300',
              isEditing ? 'flex' : 'hidden'
            )}
          >
            <DButton
              variant="grey"
              fullWidth
              onClick={() => setIsEditing(false)}
            >
              Cancel
            </DButton>
            <DButton
              variant="dark"
              fullWidth
              onClick={handleEditAccount}
              loading={isPending}
            >
              Save
            </DButton>
          </div>
        </div>
        <div className="flex flex-col gap-size5 p-size5 w-full">
          <p className="text-xl font-medium tracking-tight">Categories</p>
          <div className="flex flex-col gap-size3">
            {categories?.map((category, index) => (
              <div
                className="flex items-center justify-between border border-grey-5 rounded-size0 py-size1 px-size3"
                key={index}
              >
                <div className="flex items-center gap-size2">
                  <CategoryIcon style={{ color: category.color }} />
                  <p className="text-sm font-regular tracking-tight">
                    {category.name}
                  </p>
                </div>
                <div className="flex items-center gap-size1">
                  <button
                    onClick={() => {
                      setIsEditingCategory(true);
                      setEditingCategory(category);
                    }}
                  >
                    <EditIcon />
                  </button>
                  <button
                    onClick={() => {
                      setIsDeletingCategory(true);
                      setDeletingCategory(category);
                    }}
                  >
                    <DeleteIcon />
                  </button>
                </div>
              </div>
            ))}
            <DButton
              variant="grey"
              fullWidth
              onClick={() => setIsAddingCategory(true)}
            >
              <AddIcon />
              Add new category
            </DButton>
          </div>
        </div>
      </div>
      <AddCategory
        open={isAddingCategory}
        onClose={() => setIsAddingCategory(false)}
        handleAddCategory={handleAddCategory}
      />
      <EditCategory
        open={isEditingCategory}
        onClose={() => setIsEditingCategory(false)}
        handleEditCategory={handleEditCategory}
        category={editingCategory}
      />
      <DConfirmationModal
        open={isDeletingCategory}
        onClose={() => setIsDeletingCategory(false)}
        onConfirm={handleDeleteCategory}
        title="Delete Category"
        description="Are you sure you want to delete this category? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={isDeletingCategoryLoading}
      />
    </div>
  );
};

export default HumanHandoverSettings;
