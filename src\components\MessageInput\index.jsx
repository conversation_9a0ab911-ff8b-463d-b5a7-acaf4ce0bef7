import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import NewMicrophoneIcon from '../Global/Icons/NewMicrophoneIcon';
import NewStopRecordingIcon from '../Global/Icons/NewStopRecordingIcon';
import RealTimeVoiceButton from '../RealTimeVoiceButton';
import clsx from 'clsx';
import NewSendIcon from '../Global/Icons/NewSendIcon';
import AttachmentIcon from '../Global/Icons/AttachmentIcon';
import DButtonIcon from '../Global/DButtonIcon';
import MenuQuickResponse from '../Chatbot/MenuQuickResponse';

/**
 * MessageInput - A component for sending messages with voice recording capability
 * @param {Object} props - Component props
 * @param {function} props.onSend - Callback when a message is sent
 * @param {function} props.onRecordStart - Callback when recording starts
 * @param {function} props.onRecordStop - Callback when recording stops
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the input is disabled
 * @param {boolean} props.isRecording - External control for recording state
 * @param {function} props.setIsRecording - Function to update external recording state
 */
const MessageInput = ({
  onSend,
  onRecordStart,
  onRecordStop,
  placeholder = 'Message',
  className = '',
  disabled = false,
  isRecording = false,
  setIsRecording,
  config,
  handleShowVoice,
  isInHumanHandoverApp,
  pollResponse,
  humanHandoverConfig,
  setUploadedImages,
  uploadedImages = [],
  onTypingStart,
  onTypingStop,
  ...props
}) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [finalTranscript, setFinalTranscript] = useState('');
  const inputRef = useRef(null);
  const recognitionRef = useRef(null);
  const manualStopRef = useRef(false);
  const fileInputRef = useRef(null);

  // Typing event state
  const typingTimerRef = useRef(null);
  const lastTypingTimeRef = useRef(0);
  const typingSentRef = useRef(false);

  // Initialize speech recognition
  useEffect(() => {
    // Check if browser supports speech recognition
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported in this browser');
      return;
    }

    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();
    
    // Configure recognition
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    // Set up event handlers
    recognitionRef.current.onstart = () => {
      if (setIsRecording) setIsRecording(true);
      if (onRecordStart) onRecordStart();
    };

    recognitionRef.current.onend = () => {
      // Only stop recording if manually stopped
      if (manualStopRef.current) {
        if (setIsRecording) setIsRecording(false);
        if (onRecordStop) onRecordStop(finalTranscript || transcript);
        manualStopRef.current = false;
      } else if (isRecording) {
        // If it stopped on its own (timeout), restart it
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error('Error restarting speech recognition:', error);
          if (setIsRecording) setIsRecording(false);
        }
      }
    };

    recognitionRef.current.onresult = (event) => {
      let interimTranscript = '';
      let newFinalTranscript = finalTranscript;

      // Loop through the results to get the transcript
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcriptPiece = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          newFinalTranscript += ' ' + transcriptPiece;
          setFinalTranscript(newFinalTranscript.trim());
        } else {
          interimTranscript += transcriptPiece;
        }
      }

      // Update the transcript and input field
      const currentTranscript = (newFinalTranscript + ' ' + interimTranscript).trim();
      setTranscript(interimTranscript);
      setMessage(currentTranscript);
    };

    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error', event.error);
      if (event.error === 'no-speech') {
        // Don't stop for no-speech errors, just keep listening
        return;
      }
      if (setIsRecording) setIsRecording(false);
    };

    // Clean up on unmount
    return () => {
      if (recognitionRef.current) {
        manualStopRef.current = true; // Mark as manual stop for cleanup
        recognitionRef.current.stop();
      }

      // Clean up typing timer
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
      }
    };
  }, [onRecordStart, onRecordStop, setIsRecording]);

  // Effect to start/stop recognition based on isRecording prop changes
  useEffect(() => {
    if (!recognitionRef.current) return;
    
    if (isRecording && !recognitionRef.current.running) {
      try {
        setTranscript('');
        setFinalTranscript('');
        manualStopRef.current = false;
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    } else if (!isRecording && recognitionRef.current.running) {
      manualStopRef.current = true;
      recognitionRef.current.stop();
    }
  }, [isRecording]);

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = inputRef.current;
    if (!textarea) return;

    // Store cursor position before height changes
    const cursorStart = textarea.selectionStart;
    const cursorEnd = textarea.selectionEnd;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Calculate line count based on scrollHeight and line height
    const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight) || 24;
    const paddingTop = parseInt(window.getComputedStyle(textarea).paddingTop) || 0;
    const paddingBottom = parseInt(window.getComputedStyle(textarea).paddingBottom) || 0;
    const totalPadding = paddingTop + paddingBottom;

    // Calculate how many lines we have
    const textLines = Math.min(3, Math.max(1, Math.floor((textarea.scrollHeight - totalPadding) / lineHeight)));

    // Set the height based on the number of lines (max 3)
    const newHeight = (textLines * lineHeight) + totalPadding;
    textarea.style.height = `${newHeight}px`;

    // Restore cursor position to prevent jumping
    textarea.setSelectionRange(cursorStart, cursorEnd);

  }, [message]);

  const handleInputChange = (e) => {
    setMessage(e.target.value);

    // Maintain cursor position to prevent jumping
    const { selectionStart } = e.target;
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.setSelectionRange(selectionStart, selectionStart);
      }
    }, 0);

    // Send typing events for human handover (debounced to once every 3 seconds)
    if (isInHumanHandoverApp && onTypingStart) {
      const now = Date.now();

      // Only send typing start if we haven't sent one recently
      if (!typingSentRef.current || now - lastTypingTimeRef.current > 3000) {
        onTypingStart();
        typingSentRef.current = true;
        lastTypingTimeRef.current = now;
      }

      // Clear any existing timer
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
      }

      // Set timer to reset typing state after 3 seconds
      typingTimerRef.current = setTimeout(() => {
        typingSentRef.current = false;
      }, 3000);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend(message);
      setMessage('');
      // Clear transcript when sending message
      setTranscript('');
      setFinalTranscript('');

      // Send typing stop event when sending a message
      if (isInHumanHandoverApp && onTypingStop) {
        onTypingStop();

        // Clear typing timer
        if (typingTimerRef.current) {
          clearTimeout(typingTimerRef.current);
          typingTimerRef.current = null;
        }

        // Reset typing state
        typingSentRef.current = false;
      }
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      manualStopRef.current = true;
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    } else {
      // Start recording
      setTranscript('');
      setFinalTranscript('');
      manualStopRef.current = false;
      if (recognitionRef.current) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error('Error starting speech recognition:', error);
        }
      }
    }
  };
  const handleQuickResponseClick = (quickResponseContent) => {
    onSend?.(quickResponseContent);
    setMessage('');
    setTranscript('');
    setFinalTranscript('');
    if (inputRef.current) {
      inputRef.current.blur();
    }
    setIsFocused(false);
  };

  const handleFocus = () => {
    setIsFocused(true);
    // Scroll textarea into view after iOS keyboard opens
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }, 300); // allow iOS keyboard to open
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Handle clicking outside the input, scrolling, and cursor leaving textarea
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        inputRef.current.blur();
        setIsFocused(false);
      }
    };

    const handleScroll = () => {
      if (inputRef.current && document.activeElement === inputRef.current) {
        inputRef.current.blur();
        setIsFocused(false);
      }
    };

    const handleMouseLeave = () => {
      if (inputRef.current && document.activeElement === inputRef.current) {
        inputRef.current.blur();
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true); // Use capture phase to catch all scroll events

    // Add mouse leave event to the textarea
    if (inputRef.current) {
      inputRef.current.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      if (inputRef.current) {
        inputRef.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // Determine the container class based on state without changing alignment
  const getContainerClass = () => {
    if (isFocused) {
      return 'border border-new-accent new-bg-light';
    } else if (isRecording) {
      return 'new-bg-hover border border-new-accent';
    } else {
      return 'new-bg-button hover:new-bg-hover';
    }
  };

  const handleImageUpload = (event) => {
    if (event.target.files && event.target.files.length > 0) {
      // Add new images to the array
      const newImages = Array.from(event.target.files);
      setUploadedImages(prev => [...prev, ...newImages]);

      // Reset file input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = null;
      }
    }
  };

  // Handle sending message and clearing transcript
  const handleSendMessage = () => {
    onSend(message);
    setMessage('');
    // Clear transcript when sending message
    setTranscript('');
    setFinalTranscript('');

    // Send typing stop event when sending a message
    if (isInHumanHandoverApp && onTypingStop) {
      onTypingStop();

      // Clear typing timer
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
        typingTimerRef.current = null;
      }

      // Reset typing state
      typingSentRef.current = false;
    }
  };

  return (<div className='flex items-start gap-4 w-full'>
      <div
        className={`
          bg-btn hover:bg-hover
          ${isFocused ? 'bg-white border !border-accent hover:bg-white' : ''}
          flex items-center pl-4 pr-2 flex-1 min-h-[42px] max-h-[120px]
          rounded-size4 transition-colors duration-200 ease-in-out border border-transparent
          gap-2 flex-shrink-0
          ${getContainerClass()}
          ${className}
        `}
        {...props}
      >
        <textarea
          ref={inputRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={isRecording ? 'Listening...' : placeholder}
          disabled={disabled}
          rows={1}
          className="flex-1 bg-transparent border-none w-full resize-none h-full outline-none new-text-dark font-['Inter'] text-base font-normal leading-[160%] placeholder:text-black flex items-center"
          style={{
            minHeight: '25px',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            overflowY: 'auto',
            paddingBottom: '3.5em',
            '-webkit-overflow-scrolling': 'touch'
          }}

      
        />

        {isInHumanHandoverApp && (
          <MenuQuickResponse
            quickResponses={humanHandoverConfig?.quick_responses}
            handleQuickResponseClick={handleQuickResponseClick}
          />
        )}

        {/* Always show the attachment button */}
        {(isInHumanHandoverApp || pollResponse) && <button
          className="size-6 rounded-size0 flex items-center justify-center"
          onClick={() => fileInputRef.current?.click()}
        >
          <AttachmentIcon className="size-6 text-newGrey hover:text-accent" />
        </button>}

        {/* Always show the file input */}
        <input
          type="file"
          accept="image/*"
          multiple
          className="hidden"
          ref={fileInputRef}
          onChange={handleImageUpload}
        />

        <button
          onClick={toggleRecording}
          disabled={disabled}
          className={`flex items-center justify-center w-6 h-6 rounded-full focus:outline-none transition-colors ${isRecording ? 'text-red-500' : ''}`}
          aria-label={isRecording ? "Stop recording" : "Start recording"}
          title={isRecording ? "Stop recording" : "Start speech to text"}
        >
          {isRecording ? (
            <NewStopRecordingIcon/>
          ) : (
            <NewMicrophoneIcon  className='text-newGrey hover:text-accent' />
          )}
        </button>

      </div>
      {config?.realtime_voice_access && !isInHumanHandoverApp && !message && (
        <RealTimeVoiceButton
          onClick={() => handleShowVoice && handleShowVoice()}
          disabled={disabled || pollResponse}
        />
      )}
      {/* Show send button if there's text OR uploaded images */}
      {(message || (setUploadedImages && uploadedImages?.length > 0)) && (
        <button
          onClick={handleSendMessage}
          className={clsx(
            'inline-flex h-[42px] w-[42px] py-3 px-3 items-center justify-center',
            'rounded-full transition-colors duration-200',
            'bg-btn hover:bg-hover',
            'border-none',
            disabled && 'opacity-50 cursor-not-allowed',
          )}
          aria-label="Send message"
        >
          <NewSendIcon/>
        </button>
      )}
    </div>
  );
};

MessageInput.propTypes = {
  onSend: PropTypes.func,
  onRecordStart: PropTypes.func,
  onRecordStop: PropTypes.func,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  isRecording: PropTypes.bool,
  setIsRecording: PropTypes.func,
  config: PropTypes.object,
  handleShowVoice: PropTypes.func,
  uploadedImages: PropTypes.array,
  setUploadedImages: PropTypes.func,
  onTypingStart: PropTypes.func,
  onTypingStop: PropTypes.func
};

export default MessageInput;