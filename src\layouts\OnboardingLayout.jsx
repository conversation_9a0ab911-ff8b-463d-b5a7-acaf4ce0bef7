import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';

/**
 * Layout component for onboarding pages
 * Provides consistent navigation, blur backgrounds, and container structure
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to render inside the layout
 * @param {boolean} props.showLoginButton - Whether to show the login button in nav (default: false)
 * @param {string} props.containerClassName - Additional classes for the main container
 * @param {string} props.navClassName - Additional classes for the navigation
 */
const OnboardingLayout = ({ 
  children, 
  showLoginButton = false, 
  containerClassName = "min-h-screen overflow-hidden",
  navClassName = "max-w-[75rem] mx-auto"
}) => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll event for mobile navigation background
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={containerClassName}>
      {/* Circle blur backgrounds with custom animation */}
      <div 
        className="w-[1000px] h-[1000px] rounded-full bg-gradient-to-br from-[#1a00ff] via-[#8a00ff] to-white absolute -top-[30vh] right-[35vw] opacity-20 animate-pulse-custom blur-[1000px] z-10"
      />
      <div 
        className="w-[1000px] h-[1000px] rounded-full bg-gradient-to-tl from-[#ff3a00] via-[#ff8a00] to-white absolute bottom-0 right-0 opacity-20 animate-pulse-custom-delayed blur-[1000px] z-10" 
      />
      
      {/* Navigation */}
      <nav className={`w-full z-20 ${navClassName} absolute left-0 right-0 transition-all duration-300 bg-transparent lg:max-w-[75rem] mx-auto`}>
        <div className="h-[64px] flex items-center justify-between px-2 md:px-0">
          <a href="https://dante-ai.com/" className="block">
            <DFullLogo size="md" />
          </a>
          {showLoginButton && (
            <button 
              className="text-[#6351FF] lg:text-white rounded-size2 px-[24px] py-[10px] border-2 border-[#6351FF] lg:border-white text-sm font-medium z-30 font-[Inter] h-[48px]" 
              onClick={() => navigate('/log-in')}
            >
              Log In
            </button>
          )}
        </div>
      </nav>
      
      {/* Main content */}
      {children}
    </div>
  );
};

export default OnboardingLayout;
